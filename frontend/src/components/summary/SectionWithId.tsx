"use client"

import React from "react"
import { usePathname } from "next/navigation"
import { InView } from "react-intersection-observer"

import DataSnapshotDate from "@/components/layouts/DataSnapshotDate"

const SectionWithId = ({
  id,
  title,
  subTitle,
  children,
}: {
  id: string
  title?: string
  subTitle?: string
  children?: React.ReactNode
}) => {
  const pathname = usePathname()

  return (
    <InView
      as="div"
      id={id}
      className="mt-1 flex scroll-mt-6 flex-col gap-4"
      rootMargin="0px 0px -60% 0px"
      threshold={0.1}
      onChange={(inView) => {
        if (!inView) return

        const newHash = `#${id}`
        const newUrl = `${pathname}${newHash}`
        if (window.location.hash !== newHash) {
          history.replaceState(null, "", newUrl)
        }
      }}
    >
      {title && (
        <h2 className="-mb-1 flex flex-wrap items-baseline gap-x-4 gap-y-2 text-2xl leading-none font-medium">
          {title}
          <DataSnapshotDate className="text-muted-foreground text-xs" />
        </h2>
      )}

      {subTitle && (
        <h3 className="-mb-1 text-xl leading-none font-medium">{subTitle}</h3>
      )}

      {children}
    </InView>
  )
}

export default SectionWithId
