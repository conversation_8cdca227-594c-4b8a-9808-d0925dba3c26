"use client"

import React, { useState } from "react"
import {
  ArrowUpDown,
  Calendar,
  Columns,
  Database,
  Filter,
  GripVertical,
  Hash,
  Rows,
  Tag,
  X,
} from "lucide-react"

import {
  Aggregation,
  AGGREGATIONS,
  ATTRIBUTES,
  DrillDownConfiguration,
  DrillDownData,
  DrillDownField,
  FilterCondition,
  IconType,
  ROLES,
  WithInstanceId,
} from "@/types/drill-down"
import { getOperatorOptions, getOperatorSymbol } from "@/lib/drill-down"
import { formatAbbreviatedCurrency, formatCurrency } from "@/lib/number"
import { cn } from "@/lib/utils"
import { Autocomplete } from "@/components/ui/autocomplete"
import { Button } from "@/components/ui/button"
import { MultiSelect } from "@/components/ui/multi-select"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"

export const getDisplayValue = (
  value: string | number | undefined,
  precision: number
) => {
  if (!value) return ""

  const numericValue = Number(value)
  const displayValue = !isNaN(numericValue)
    ? formatAbbreviatedCurrency(numericValue, precision)
    : value

  return displayValue
}

export const FieldIcon = ({ iconType }: { iconType: IconType }) => {
  switch (iconType) {
    case "calendar":
      return <Calendar className="size-3" />
    case "hash":
      return <Hash className="size-3" />
    default:
      return <Tag className="size-3" />
  }
}

export const AvailableFields = ({
  title,
  fields,
  handleDragStart,
  handleDragEnd,
}: {
  title: string
  fields: DrillDownField[]
  handleDragStart: (event: React.DragEvent, field: DrillDownField) => void
  handleDragEnd: () => void
}) => (
  <div className="grid gap-1">
    <div className="text-muted-foreground text-xs">{title}</div>

    <div className="flex flex-wrap gap-1">
      {fields.map((field) => (
        <div
          key={field.id}
          className="flex cursor-move items-center gap-1.5 bg-neutral-100 px-2 py-1 text-xs text-neutral-800 transition-colors hover:bg-neutral-200 hover:text-neutral-900"
          onDragStart={(event) => handleDragStart(event, field)}
          onDragEnd={handleDragEnd}
          draggable
        >
          <FieldIcon iconType={field.iconType} />
          {field.name}
          <GripVertical className="size-3 opacity-50" />
        </div>
      ))}
    </div>
  </div>
)

export const ConfigurationFields = ({
  title,
  zone,
  handleDragOver,
  handleDrop,
  handleDragLeave,
  isDragOver,
  isEmpty,
  clearFields,
  swapRowsAndColumns,
  children,
}: {
  title: string
  zone: keyof DrillDownConfiguration
  handleDragOver: (
    event: React.DragEvent,
    zone: keyof DrillDownConfiguration
  ) => void
  handleDrop: (
    event: React.DragEvent,
    zone: keyof DrillDownConfiguration
  ) => void
  handleDragLeave: () => void
  isDragOver: boolean
  isEmpty: boolean
  clearFields?: (zone: keyof DrillDownConfiguration) => void
  swapRowsAndColumns?: () => void
  children: React.ReactNode
}) => (
  <div className="grid gap-1">
    <div className="text-muted-foreground flex items-center gap-1.5 [&_svg]:size-3">
      {zone === "filters" ? (
        <Filter />
      ) : zone === "rows" ? (
        <Rows />
      ) : (
        <Columns />
      )}

      <p className="text-xs">{title}</p>

      <div className="ml-auto flex items-center gap-0.5">
        {swapRowsAndColumns && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="size-4 hover:bg-transparent"
                onClick={swapRowsAndColumns}
              >
                <ArrowUpDown className="size-3" />
              </Button>
            </TooltipTrigger>

            <TooltipContent side="left">Swap Rows/Columns</TooltipContent>
          </Tooltip>
        )}

        {clearFields && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="size-4 hover:bg-transparent"
                onClick={() => clearFields(zone)}
              >
                <X className="size-3" />
              </Button>
            </TooltipTrigger>

            <TooltipContent side="left" className="capitalize">
              Clear {zone}
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </div>

    <div
      className={cn(
        "min-h-11 border-2 border-dashed p-2 transition-colors",
        isDragOver
          ? "border-primary bg-primary/10 text-primary!"
          : "border-border",
        isEmpty &&
          "text-muted-foreground flex items-center justify-center text-xs"
      )}
      onDragOver={(event) => handleDragOver(event, zone)}
      onDragLeave={handleDragLeave}
      onDrop={(event) => handleDrop(event, zone)}
    >
      {isEmpty ? (
        `Drop ${zone} fields here`
      ) : (
        <div className="flex flex-wrap gap-1">{children}</div>
      )}
    </div>
  </div>
)

export const FilterChip = ({
  data,
  filter,
  updateField,
  removeField,
}: {
  data: DrillDownData[]
  filter: WithInstanceId<FilterCondition>
  updateField: (
    field: WithInstanceId<DrillDownField>,
    zone: keyof DrillDownConfiguration,
    filter?: WithInstanceId<FilterCondition>
  ) => void
  removeField: (id: string, zone: keyof DrillDownConfiguration) => void
}) => {
  const [isEditing, setIsEditing] = useState<boolean>(false)
  const [operator, setOperator] = useState<FilterCondition["operator"]>(
    filter.operator
  )
  const [value, setValue] = useState<FilterCondition["value"]>(filter.value)
  const [aggregation, setAggregation] = useState<Aggregation | undefined>(
    filter.aggregation
  )

  const startEditing = () => {
    setOperator(filter.operator)
    setValue(filter.value)
    setAggregation(filter.aggregation)
    setIsEditing(true)
  }

  const handleSave = () => {
    let finalValue = value

    if (filter.field.dataType === "number") {
      if (Array.isArray(finalValue)) {
        finalValue = finalValue.map((v) => (v === "" ? "" : Number(v)))
      } else {
        finalValue = finalValue === "" ? "" : Number(finalValue)
      }
    }

    updateField(
      {
        ...filter.field,
        instanceId: filter.instanceId,
      },
      "filters",
      {
        instanceId: filter.instanceId,
        field: filter.field,
        operator,
        value: finalValue,
        aggregation,
      }
    )

    setIsEditing(false)
  }

  const handleCancel = () => {
    setOperator(filter.operator)
    setValue(filter.value)
    setAggregation(filter.aggregation)
    setIsEditing(false)
  }

  const listSupported =
    filter.field.dataType !== "number" ||
    filter.field.id === "year" ||
    filter.field.id === "month"

  const renderValueInput = () => {
    const options = listSupported
      ? [...new Set(data.map((i) => String(i[filter.field.id] ?? "")))]
          .filter(Boolean)
          .sort((a, b) =>
            !isNaN(Number(a)) && !isNaN(Number(b))
              ? Number(a) - Number(b)
              : a.localeCompare(b)
          )
      : []

    if (operator === "between" || operator === "notBetween") {
      const valArray = Array.isArray(value) ? value : ["", ""]

      return (
        <div className="flex gap-2">
          <FilterInputAutocomplete
            value={valArray[0]}
            setValue={(v) => setValue([String(v), valArray[1]])}
            placeholder="Min"
            options={options}
          />
          <FilterInputAutocomplete
            value={valArray[1]}
            setValue={(v) => setValue([valArray[0], String(v)])}
            placeholder="Max"
            options={options}
          />
        </div>
      )
    }

    if (operator === "in" || operator === "notIn") {
      return (
        <MultiSelect
          className="bg-background w-full rounded-none border-orange-300 hover:text-orange-800 focus:border-orange-500! focus:ring-orange-500! focus:outline-none"
          placeholder="Select"
          value={(Array.isArray(value) ? value : [value]).map((v) => String(v))}
          onChange={(value) => setValue(value)}
          options={options}
          modal
        />
      )
    }

    return (
      <FilterInputAutocomplete
        value={value}
        setValue={setValue}
        placeholder="Search..."
        options={options}
      />
    )
  }

  const isRangeOp = operator === "between" || operator === "notBetween"
  const isListOp = operator === "in" || operator === "notIn"

  const isSaveDisabled = (() => {
    if (isRangeOp) {
      return (
        !Array.isArray(value) ||
        value.length !== 2 ||
        value[0] === "" ||
        value[1] === ""
      )
    } else if (isListOp) {
      if (Array.isArray(value)) return value.length === 0
      if (typeof value === "string") return value.trim() === ""
      return true
    } else {
      return value === "" || value == null
    }
  })()

  const formatIfNumber = (val: string | number, field: DrillDownField) => {
    if (field.role === "measures" && val !== "" && !isNaN(Number(val)))
      return formatCurrency(Number(val), 2)
    return val || "..."
  }

  return isEditing ? (
    <div className="grid w-full gap-3 border border-orange-300 bg-orange-50 p-2 text-orange-800">
      <div className="flex items-center gap-2 text-orange-900">
        <div className="flex size-5 items-center justify-center bg-orange-200">
          <FieldIcon iconType={filter.field.iconType} />
        </div>
        <p className="text-sm font-semibold">{filter.field.name}</p>
      </div>

      <div className="grid gap-1">
        <label className="text-xs font-medium">Condition</label>
        <Select
          value={operator}
          onValueChange={(newOp) => {
            const oldOp = operator
            setOperator(newOp as FilterCondition["operator"])

            const isRangeOp = (op: string) =>
              op === "between" || op === "notBetween"
            const isListOp = (op: string) => op === "in" || op === "notIn"
            const isSingleOp = (op: string) => !isRangeOp(op) && !isListOp(op)

            if (
              (isRangeOp(newOp) && isRangeOp(oldOp)) ||
              (isListOp(newOp) && isListOp(oldOp)) ||
              (isSingleOp(newOp) && isSingleOp(oldOp))
            ) {
              return
            }

            if (isRangeOp(newOp)) setValue(["", ""])
            else if (isListOp(newOp)) setValue([])
            else setValue("")
          }}
        >
          <SelectTrigger className="bg-background w-full rounded-none border-orange-300 focus:border-orange-500! focus:ring-orange-500! focus:outline-none [&>svg]:text-orange-800!">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {getOperatorOptions(filter.field.dataType)
              .filter(
                (option) =>
                  listSupported || !["in", "notIn"].includes(option.value)
              )
              .map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid gap-1">
        <label className="text-xs font-medium">Value</label>
        {renderValueInput()}
      </div>

      {filter.field.role === "measures" && (
        <div className="grid gap-1">
          <label className="text-xs font-medium">Aggregation (Optional)</label>
          <Select
            value={aggregation || ""}
            onValueChange={(value) =>
              setAggregation((value || "") as Aggregation)
            }
          >
            <SelectTrigger className="bg-background w-full rounded-none border-orange-300 focus:border-orange-500! focus:ring-orange-500! focus:outline-none [&>svg]:text-orange-800!">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={null!}>Select</SelectItem>
              {AGGREGATIONS.map((agg) => (
                <SelectItem key={agg} value={agg}>
                  {agg.toUpperCase()}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <p className="text-xs">
            Filters aggregated values instead of raw data if selected.
          </p>
        </div>
      )}

      {(() => {
        if (
          isRangeOp &&
          (!Array.isArray(value) || value.every((v) => v === ""))
        )
          return null
        if (
          isListOp &&
          ((Array.isArray(value) && value.length === 0) ||
            (typeof value === "string" && value.trim() === ""))
        )
          return null
        if (!isRangeOp && !isListOp && (value === "" || value == null))
          return null

        return (
          <div className="-mb-1 flex flex-wrap items-center gap-0.5 border-t border-orange-200 pt-2 text-xs">
            <span className="font-medium">Preview:</span>
            <span>{filter.field.name}</span>
            <span className="lowercase">
              {
                getOperatorOptions(filter.field.dataType).find(
                  (opt) => opt.value === operator
                )?.label
              }
            </span>
            <span className="bg-orange-200 px-1 py-0.5 font-medium">
              {aggregation ? `${aggregation.toUpperCase()} ` : ""}
              {Array.isArray(value)
                ? isRangeOp
                  ? value
                      .map((v) => formatIfNumber(v, filter.field))
                      .join(" - ")
                  : value.map((v) => formatIfNumber(v, filter.field)).join(", ")
                : formatIfNumber(value, filter.field)}
            </span>
          </div>
        )
      })()}

      <div className="flex gap-2 border-t border-orange-200 pt-3">
        <Button
          size="sm"
          className="flex-1 rounded-none bg-orange-600 hover:bg-orange-700 focus:border-orange-500! focus:ring-orange-500! focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
          onClick={handleSave}
          disabled={isSaveDisabled}
        >
          Save
        </Button>
        <Button
          size="sm"
          variant="outline"
          className="flex-1 rounded-none border-orange-300 hover:bg-orange-200 hover:text-orange-900 focus:border-orange-500! focus:ring-orange-500! focus:outline-none"
          onClick={handleCancel}
        >
          Cancel
        </Button>
      </div>
    </div>
  ) : (
    <div
      className="flex cursor-pointer items-center gap-1 bg-orange-100 px-2 py-1 text-xs text-orange-800 transition-colors hover:bg-orange-200 hover:text-orange-900"
      onClick={startEditing}
    >
      <FieldIcon iconType={filter.field.iconType} />
      <span>{filter.field.name}</span>
      <span className="opacity-70">{getOperatorSymbol(filter.operator)}</span>
      <span>
        {filter.aggregation ? `${filter.aggregation.toUpperCase()} ` : ""}
        {Array.isArray(filter.value)
          ? filter.operator === "between" || filter.operator === "notBetween"
            ? filter.value
                .map((v) => formatIfNumber(v, filter.field))
                .join(" - ")
            : filter.value
                .map((v) => formatIfNumber(v, filter.field))
                .join(", ")
          : formatIfNumber(filter.value, filter.field)}
      </span>

      <div
        className="-my-1 -mr-2 cursor-pointer py-1 pr-2 opacity-70 transition-opacity hover:opacity-100"
        onClick={(event) => {
          event.stopPropagation()
          removeField(filter.instanceId, "filters")
        }}
      >
        <X className="size-3" />
      </div>
    </div>
  )
}

const FilterInputAutocomplete = ({
  value,
  setValue,
  placeholder,
  options,
}: {
  value: FilterCondition["value"]
  setValue: React.Dispatch<React.SetStateAction<FilterCondition["value"]>>
  placeholder: string
  options: string[]
}) => (
  <Autocomplete
    className="bg-background w-full rounded-none border-orange-300 text-orange-800 focus:border-orange-500! focus:ring-orange-500! focus:outline-none"
    value={String(value)}
    onValueChange={(v) => setValue(v)}
    onSelectValue={(v) => setValue(v)}
    options={options.filter((option) =>
      option.toLowerCase().includes(value.toString().toLowerCase())
    )}
    placeholder={placeholder}
    modal
  />
)

export const ConfigurationChip = ({
  field,
  zone,
  updateField,
  removeField,
  reorderField,
}: {
  field: WithInstanceId<DrillDownField>
  zone: keyof DrillDownConfiguration
  updateField: (
    field: WithInstanceId<DrillDownField>,
    zone: keyof DrillDownConfiguration
  ) => void
  removeField: (instanceId: string, zone: keyof DrillDownConfiguration) => void
  reorderField: (
    draggedInstanceId: string,
    targetInstanceId: string,
    zone: keyof DrillDownConfiguration
  ) => void
}) => {
  const [isEditing, setIsEditing] = useState<boolean>(false)
  const [attribute, setAttribute] = useState<DrillDownField["attribute"]>(
    field.attribute
  )
  const [role, setRole] = useState<DrillDownField["role"]>(field.role)
  const [aggregation, setAggregation] = useState<DrillDownField["aggregation"]>(
    field.aggregation
  )

  const isEditDisabled = field.dataType !== "number"

  const startEditing = () => {
    if (isEditDisabled) return

    setAttribute(field.attribute)
    setRole(field.role)
    setAggregation(field.aggregation)
    setIsEditing(true)
  }

  const handleSave = () => {
    updateField(
      {
        ...field,
        attribute,
        role,
        aggregation,
      },
      zone
    )

    setIsEditing(false)
  }

  const handleCancel = () => {
    setAttribute(field.attribute)
    setRole(field.role)
    setAggregation(field.aggregation)
    setIsEditing(false)
  }

  const handleDragStart = (event: React.DragEvent) => {
    event.dataTransfer.setData("text/plain", field.instanceId)
    event.dataTransfer.effectAllowed = "move"
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = "move"
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    const draggedInstanceId = event.dataTransfer.getData("text/plain")
    if (draggedInstanceId && draggedInstanceId !== field.instanceId) {
      reorderField(draggedInstanceId, field.instanceId, zone)
    }
  }

  return isEditing ? (
    <div
      className={cn(
        "grid w-full gap-3 border p-2",
        attribute === "continuous"
          ? "border-green-300 bg-green-50 text-green-800"
          : "border-blue-300 bg-blue-50 text-blue-800"
      )}
    >
      <div
        className={cn(
          "flex items-center gap-2",
          attribute === "continuous" ? "text-green-900" : "text-blue-900"
        )}
      >
        <div
          className={cn(
            "flex size-5 items-center justify-center",
            attribute === "continuous" ? "bg-green-200" : "bg-blue-200"
          )}
        >
          <FieldIcon iconType={field.iconType} />
        </div>

        <p className="text-sm font-semibold">{field.name}</p>
      </div>

      <div className="grid gap-1">
        <label className="text-xs font-medium">Role</label>

        <Select
          value={role}
          onValueChange={(value) => {
            const role = value as DrillDownField["role"]
            setRole(role)

            if (role === "measures") {
              setAggregation("sum")
            } else {
              setAggregation(undefined)
            }
          }}
        >
          <SelectTrigger
            className={cn(
              "bg-background w-full rounded-none capitalize",
              attribute === "continuous"
                ? "border-green-300 focus:border-green-500! focus:ring-green-500! focus:outline-none [&>svg]:text-green-800!"
                : "border-blue-300 focus:border-blue-500! focus:ring-blue-500! focus:outline-none [&>svg]:text-blue-800!"
            )}
          >
            <SelectValue />
          </SelectTrigger>

          <SelectContent>
            {ROLES.map((option) => (
              <SelectItem key={option} value={option} className="capitalize">
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid gap-1">
        <label className="text-xs font-medium">Attribute</label>

        <Select
          value={attribute}
          onValueChange={(value) =>
            setAttribute(value as DrillDownField["attribute"])
          }
        >
          <SelectTrigger
            className={cn(
              "bg-background w-full rounded-none capitalize",
              attribute === "continuous"
                ? "border-green-300 focus:border-green-500! focus:ring-green-500! focus:outline-none [&>svg]:text-green-800!"
                : "border-blue-300 focus:border-blue-500! focus:ring-blue-500! focus:outline-none [&>svg]:text-blue-800!"
            )}
          >
            <SelectValue />
          </SelectTrigger>

          <SelectContent>
            {ATTRIBUTES.map((option) => (
              <SelectItem key={option} value={option} className="capitalize">
                {option}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {role === "measures" && (
        <div className="grid gap-1">
          <label className="text-xs font-medium">Aggregation</label>

          <Select
            value={aggregation}
            onValueChange={(value) =>
              setAggregation(value as DrillDownField["aggregation"])
            }
          >
            <SelectTrigger
              className={cn(
                "bg-background w-full rounded-none capitalize",
                attribute === "continuous"
                  ? "border-green-300 focus:border-green-500! focus:ring-green-500! focus:outline-none [&>svg]:text-green-800!"
                  : "border-blue-300 focus:border-blue-500! focus:ring-blue-500! focus:outline-none [&>svg]:text-blue-800!"
              )}
            >
              <SelectValue placeholder="Select" />
            </SelectTrigger>

            <SelectContent>
              {AGGREGATIONS.map((option) => (
                <SelectItem key={option} value={option} className="capitalize">
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      <div
        className={cn(
          "flex gap-2 border-t pt-3",
          attribute === "continuous" ? "border-green-200" : "border-blue-200"
        )}
      >
        <Button
          size="sm"
          className={cn(
            "flex-1 rounded-none",
            attribute === "continuous"
              ? "bg-green-600 hover:bg-green-700 focus:border-green-500! focus:ring-green-500! focus:outline-none"
              : "bg-blue-600 hover:bg-blue-700 focus:border-blue-500! focus:ring-blue-500! focus:outline-none"
          )}
          onClick={handleSave}
        >
          Save
        </Button>

        <Button
          size="sm"
          variant="outline"
          className={cn(
            "flex-1 rounded-none",
            attribute === "continuous"
              ? "border-green-300 hover:bg-green-200 hover:text-green-900 focus:border-green-500! focus:ring-green-500! focus:outline-none"
              : "border-blue-300 hover:bg-blue-200 hover:text-blue-900 focus:border-blue-500! focus:ring-blue-500! focus:outline-none"
          )}
          onClick={handleCancel}
        >
          Cancel
        </Button>
      </div>
    </div>
  ) : (
    <div
      className={cn(
        "flex items-center gap-1 px-2 py-1 text-xs transition-colors",
        field.attribute === "continuous"
          ? [
              "bg-green-100 text-green-800",
              !isEditDisabled && "hover:bg-green-200 hover:text-green-900",
            ]
          : [
              "bg-blue-100 text-blue-800",
              !isEditDisabled && "hover:bg-blue-200 hover:text-blue-900",
            ],
        !isEditDisabled && "cursor-pointer"
      )}
      onClick={startEditing}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      draggable
    >
      <FieldIcon iconType={field.iconType} />

      <span>
        {field.role === "measures" && field.aggregation
          ? `${field.aggregation.toUpperCase()}(${field.name})`
          : field.name}
      </span>

      <div
        className="-my-1 -mr-2 cursor-pointer py-1 pr-2 opacity-70 transition-opacity hover:opacity-100"
        onClick={(event) => {
          event.stopPropagation()
          removeField(field.instanceId, zone)
        }}
      >
        <X className="size-3" />
      </div>
    </div>
  )
}

export const EmptyState = ({
  availableFields,
}: {
  availableFields: DrillDownField[]
}) => (
  <div className="flex flex-1 flex-col items-center justify-center gap-4 text-center">
    <Database className="text-muted-foreground size-12" />

    <h3 className="text-lg font-semibold">Build Your Analysis</h3>

    <p className="text-muted-foreground -mt-2 mb-2 max-w-md">
      Drag fields from the sidebar to create dynamic drill down tables and
      charts. Start by adding the fields to columns or rows.
    </p>

    <div className="text-muted-foreground flex flex-col gap-1 text-sm">
      <div className="mb-0.5 font-bold">Quick Start:</div>

      {availableFields.filter((f) => f.role === "dimensions").length > 0 && (
        <p>
          • Drag{" "}
          {`"${availableFields.filter((f) => f.role === "dimensions")[0].name}"`}{" "}
          to Columns
        </p>
      )}

      {availableFields.filter((f) => f.role === "measures").length > 0 && (
        <p>
          • Drag{" "}
          {`"${availableFields.filter((f) => f.role === "measures")[0].name}"`}{" "}
          to Rows
        </p>
      )}

      <p>• Toggle to Chart View</p>
    </div>
  </div>
)

export const NoDataState = () => (
  <div className="text-muted-foreground flex flex-1 items-center justify-center text-center text-sm">
    No data available
  </div>
)
