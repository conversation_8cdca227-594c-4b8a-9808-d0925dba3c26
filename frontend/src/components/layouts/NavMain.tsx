"use client"

import React from "react"
import Link from "next/link"
import { usePathname, useSearchParams } from "next/navigation"
import {
  // BookOpen,
  ChartBar,
  ChartColumn,
  CheckSquare,
  ChevronRight,
  CircleDollarSign,
  FolderKanban,
  Users,
  Wallet,
  type LucideIcon,
} from "lucide-react"
import { useTranslations } from "next-intl"

import { Badge } from "@/components/ui/badge"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { usePendingActionCount } from "@/services/action"

type Page = {
  titleKey: string
  url: string
  icon: LucideIcon
  subPages?: Page[]
}

const PAGES: Page[] = [
  {
    titleKey: "summary",
    url: "/summary",
    icon: ChartColumn,
    subPages: [
      {
        titleKey: "profitAndLoss",
        url: "/profit-and-loss",
        icon: Wallet,
      },
      {
        titleKey: "creditAndBalanceSheet",
        url: "/credit-and-balance-sheet",
        icon: Wallet,
      },
      {
        titleKey: "operationsAndHr",
        url: "/operations-and-hr",
        icon: Wallet,
      },
      {
        titleKey: "patients",
        url: "/patients",
        icon: Wallet,
      },
      {
        titleKey: "insights",
        url: "/insights",
        icon: Wallet,
      },
    ],
  },
  {
    titleKey: "performance",
    url: "/performance",
    icon: CircleDollarSign,
  },
  {
    titleKey: "chartRepository",
    url: "/chart-repository",
    icon: FolderKanban,
    subPages: [
      {
        titleKey: "pnl",
        url: "/pnl",
        icon: ChartBar,
      },
      {
        titleKey: "cash",
        url: "/cash",
        icon: ChartBar,
      },
      {
        titleKey: "businessUnit",
        url: "/business-unit",
        icon: ChartBar,
      },
      {
        titleKey: "patient",
        url: "/patient",
        icon: ChartBar,
      },
      {
        titleKey: "hr",
        url: "/hr",
        icon: ChartBar,
      },
      {
        titleKey: "operations",
        url: "/operations",
        icon: ChartBar,
      },
      {
        titleKey: "growth",
        url: "/growth",
        icon: ChartBar,
      },
    ],
  },
  {
    titleKey: "analysis",
    url: "/analysis",
    icon: ChartBar,
  },
]

const BOTTOM_PAGES: Page[] = [
  {
    titleKey: "actions",
    url: "/actions",
    icon: CheckSquare,
  },
  // {
  //   titleKey: "knowledgeBase",
  //   url: "/knowledge-base",
  //   icon: BookOpen,
  // },
  {
    titleKey: "team",
    url: "/team",
    icon: Users,
  },
]

const renderPageItems = (
  pages: Page[],
  pathname: string,
  querySuffix: string,
  setOpenMobile: (open: boolean) => void,
  t: (key: string) => string,
  pendingCount?: number
) => {
  return pages.map((page) => {
    const pageTitle = t(page.titleKey)

    if (!page.subPages) {
      const isActive = pathname === page.url

      return (
        <SidebarMenuItem key={page.titleKey}>
          <SidebarMenuButton
            tooltip={pageTitle}
            className="data-[active=true]:text-primary data-[active=true]:bg-background data-[active=true]:shadow-sm"
            isActive={isActive}
            onClick={() => setOpenMobile(false)}
            asChild
          >
            <Link href={page.url}>
              <page.icon />
              <span>{pageTitle}</span>
              {page.titleKey === "actions" &&
                typeof pendingCount === "number" &&
                pendingCount > 0 && (
                  <Badge
                    variant="secondary"
                    className="ml-auto h-5 min-w-5 text-xs"
                  >
                    {pendingCount}
                  </Badge>
                )}
            </Link>
          </SidebarMenuButton>
        </SidebarMenuItem>
      )
    }

    const defaultOpen = page.subPages.some(
      (subPage) => pathname === page.url + subPage.url
    )

    return (
      <Collapsible
        key={page.titleKey}
        title={pageTitle}
        className="group/collapsible"
        defaultOpen={defaultOpen}
      >
        <SidebarMenuItem>
          <CollapsibleTrigger asChild>
            <SidebarMenuButton tooltip={pageTitle}>
              <page.icon />
              <span>{pageTitle}</span>
              <ChevronRight className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-90" />
            </SidebarMenuButton>
          </CollapsibleTrigger>

          <CollapsibleContent>
            <SidebarGroupContent>
              <SidebarMenuSub className="group-data-[collapsible=icon]:mx-0 group-data-[collapsible=icon]:mt-1 group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:gap-1 group-data-[collapsible=icon]:border-none group-data-[collapsible=icon]:p-0">
                {page.subPages.map((subPage) => {
                  const subPageTitle = t(subPage.titleKey)
                  const isSubPageActive = pathname === page.url + subPage.url

                  return (
                    <SidebarMenuSubItem key={subPage.titleKey}>
                      <SidebarMenuSubButton
                        tooltip={subPageTitle}
                        className="data-[active=true]:text-primary data-[active=true]:bg-background data-[active=true]:[&>svg]:text-primary group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:size-8! data-[active=true]:shadow-sm"
                        onClick={() => setOpenMobile(false)}
                        isActive={isSubPageActive}
                        asChild
                      >
                        <Link href={page.url + subPage.url + querySuffix}>
                          <subPage.icon className="hidden group-data-[collapsible=icon]:block" />
                          <span>{subPageTitle}</span>
                        </Link>
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  )
                })}
              </SidebarMenuSub>
            </SidebarGroupContent>
          </CollapsibleContent>
        </SidebarMenuItem>
      </Collapsible>
    )
  })
}

const NavMain = () => {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const t = useTranslations("navigation")

  const queryString = searchParams.toString()
  const querySuffix = queryString ? `?${queryString}` : ""

  const { setOpenMobile } = useSidebar()
  const { data: pendingActionCount } = usePendingActionCount()

  return (
    <>
      {/* Main Navigation */}
      <SidebarGroup>
        <SidebarMenu>
          {renderPageItems(PAGES, pathname, querySuffix, setOpenMobile, t)}
        </SidebarMenu>
      </SidebarGroup>

      {/* Bottom Navigation Items */}
      <SidebarGroup className="mt-auto">
        <SidebarMenu>
          {renderPageItems(
            BOTTOM_PAGES,
            pathname,
            querySuffix,
            setOpenMobile,
            t,
            pendingActionCount?.count
          )}
        </SidebarMenu>
      </SidebarGroup>
    </>
  )
}

export default NavMain
