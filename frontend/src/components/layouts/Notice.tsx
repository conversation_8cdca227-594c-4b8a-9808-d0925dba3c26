"use client"

import React, { useState } from "react"
import { Info, X } from "lucide-react"
import { useLocale, useTranslations } from "next-intl"

import { Alert, AlertDescription } from "@/components/ui/alert"
import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { setNoticeDismissed } from "@/components/layouts/NoticeAction"

interface NoticeProps {
  defaultShow: boolean
  finalizationDate?: Date | string
}

const Notice = ({ defaultShow, finalizationDate }: NoticeProps) => {
  const [dismissed, setDismissed] = useState<boolean>(!defaultShow)
  const locale = useLocale()
  const tNotice = useTranslations("notice")
  const tCommon = useTranslations("common")

  const handleDismiss = () => {
    setNoticeDismissed()
    setDismissed(true)
  }

  if (dismissed) return null

  // Format the date based on locale
  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === "string" ? new Date(date) : date

    if (locale === "zh") {
      // Chinese format: YYYY年MM月DD日
      return dateObj.toLocaleDateString("zh-CN", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    } else {
      // English format: DD Month YYYY
      return dateObj.toLocaleDateString("en-GB", {
        day: "numeric",
        month: "long",
        year: "numeric",
      })
    }
  }

  const formattedDate = finalizationDate ? formatDate(finalizationDate) : ""

  return (
    <div className="bg-card">
      <Alert className="bg-destructive/10 border-destructive text-destructive grid-cols-[calc(var(--spacing)*4)_1fr_auto]!">
        <Info />

        <AlertDescription className="text-destructive">
          {tNotice("financialDataNote", { date: formattedDate })}
        </AlertDescription>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              size="icon"
              variant="ghost"
              className="hover:bg-destructive/10 hover:text-destructive -mx-1 -my-0.5 size-6"
              onClick={handleDismiss}
            >
              <X />
            </Button>
          </TooltipTrigger>

          <TooltipContent
            side="bottom"
            className="bg-destructive"
            arrowClassName="bg-destructive fill-destructive"
          >
            {tCommon("dismiss")}
          </TooltipContent>
        </Tooltip>
      </Alert>
    </div>
  )
}

export default Notice
