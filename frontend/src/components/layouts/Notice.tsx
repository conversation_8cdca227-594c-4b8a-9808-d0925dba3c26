"use client"

import React, { useState } from "react"
import { Info, X } from "lucide-react"
import { useTranslations } from "next-intl"

import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { setNoticeDismissed } from "@/components/layouts/NoticeAction"

const Notice = ({ defaultShow }: { defaultShow: boolean }) => {
  const [dismissed, setDismissed] = useState<boolean>(!defaultShow)
  const tNotice = useTranslations("notice")
  const tCommon = useTranslations("common")

  const handleDismiss = () => {
    setNoticeDismissed()
    setDismissed(true)
  }

  if (dismissed) return null

  return (
    <div className="bg-card">
      <Alert className="bg-destructive/10 border-destructive text-destructive grid-cols-[calc(var(--spacing)*4)_1fr_auto]!">
        <Info />

        <AlertDescription className="text-destructive">
          {tNotice("financialDataNote")}
        </AlertDescription>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              size="icon"
              variant="ghost"
              className="hover:bg-destructive/10 hover:text-destructive -mx-1 -my-0.5 size-6"
              onClick={handleDismiss}
            >
              <X />
            </Button>
          </TooltipTrigger>

          <TooltipContent
            side="bottom"
            className="bg-destructive"
            arrowClassName="bg-destructive fill-destructive"
          >
            {tCommon("dismiss")}
          </TooltipContent>
        </Tooltip>
      </Alert>
    </div>
  )
}

export default Notice
