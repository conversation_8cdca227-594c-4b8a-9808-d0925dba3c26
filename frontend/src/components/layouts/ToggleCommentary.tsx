"use client"

import React from "react"
import { useTranslations } from "next-intl"
import { parseAsBoolean, useQueryState } from "nuqs"

import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"

export const useWithCommentary = () =>
  useQueryState("commentary", parseAsBoolean.withDefault(false))

const ToggleCommentary = () => {
  const [withCommentary, setWithCommentary] = useWithCommentary()
  const t = useTranslations("common")

  return (
    <div className="flex items-center gap-2">
      <Switch
        id="commentary"
        checked={withCommentary}
        onCheckedChange={setWithCommentary}
      />

      <Label htmlFor="commentary" className="cursor-pointer">
        {t("withCommentary")}
      </Label>
    </div>
  )
}

export default ToggleCommentary
