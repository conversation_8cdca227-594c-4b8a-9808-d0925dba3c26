"use client"

import { useState } from "react"
import { ChevronsUpDown, Loader2, LogOut } from "lucide-react"
import { useTranslations } from "next-intl"
import { toast } from "sonner"

import { useRouter } from "@/hooks/use-router"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { useAuth } from "@/contexts/auth"
import api from "@/services/api"

const NavUser = () => {
  const { isMobile } = useSidebar()

  if (isMobile) return null

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground overflow-visible group-data-[collapsible=icon]:rounded-full"
            >
              <UserInfo />
              <ChevronsUpDown className="text-muted-foreground ml-auto size-4 group-data-[collapsible=icon]:hidden" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>

          <AvatarDropdownMenu />
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}

export default NavUser

const UserInfo = () => {
  const { user } = useAuth()
  const fullName = `${user.first_name} ${user.last_name}`.trim()

  return (
    <>
      <Avatar className="size-9 rounded-full group-data-[collapsible=icon]:size-8">
        <AvatarFallback className="rounded-full">
          {(fullName || user.email || "U")[0]}
        </AvatarFallback>
      </Avatar>

      <div className="flex flex-1 flex-col truncate">
        <span className="truncate text-sm font-semibold">{fullName}</span>
        <span className="truncate text-xs">{user.email}</span>
      </div>
    </>
  )
}

export const AvatarDropdownMenu = () => {
  const { push } = useRouter()
  const { isMobile } = useSidebar()
  const t = useTranslations("user")

  const [isLoading, setIsLoading] = useState<boolean>(false)

  const handleSignOut = async () => {
    setIsLoading(true)

    try {
      await api.get("/user/user-session/v1/logout")
      push("/sign-in")
    } catch (error) {
      toast.error(String(error))
    }

    setIsLoading(false)
  }

  return (
    <DropdownMenuContent
      className="w-[--radix-dropdown-menu-trigger-width] min-w-52 rounded-lg"
      side={isMobile ? "bottom" : "right"}
      align="end"
      sideOffset={4}
    >
      <DropdownMenuLabel className="font-normal">
        <div className="flex items-center gap-2">
          <UserInfo />
        </div>
      </DropdownMenuLabel>

      <DropdownMenuSeparator />

      <DropdownMenuGroup>
        <DropdownMenuItem
          onSelect={(e) => e.preventDefault()}
          onClick={handleSignOut}
          disabled={isLoading}
        >
          {isLoading ? <Loader2 className="animate-spin" /> : <LogOut />}
          {t("signOut")}
        </DropdownMenuItem>
      </DropdownMenuGroup>
    </DropdownMenuContent>
  )
}
