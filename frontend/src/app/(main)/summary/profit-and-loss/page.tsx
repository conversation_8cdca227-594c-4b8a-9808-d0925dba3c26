import React from "react"
import { cookies } from "next/headers"

import Notice from "@/components/layouts/Notice"
import BusinessOverview from "@/components/summary/BusinessOverview"
import EBITDA from "@/components/summary/profit-and-loss/EBITDA"
import EBITDABreakdownBySegment from "@/components/summary/profit-and-loss/EBITDABreakdownBySegment"
import Profit from "@/components/summary/profit-and-loss/Profit"
import ProfitBreakdownBySegment from "@/components/summary/profit-and-loss/ProfitBreakdownBySegment"
import Revenue from "@/components/summary/profit-and-loss/Revenue"
import RevenueBreakdown from "@/components/summary/profit-and-loss/RevenueBreakdown"
import RevenueBreakdownBySegment from "@/components/summary/profit-and-loss/RevenueBreakdownBySegment"
import RevenueEBITDAProfitMargin from "@/components/summary/profit-and-loss/RevenueEBITDAProfitMargin"

export const metadata = {
  title: "Profit & Loss | Summary",
}

const ProfitAndLoss = async () => {
  const cookieStore = await cookies()
  const noticeState = cookieStore.get("notice_state")?.value ?? "true"
  const defaultShow = noticeState === "true"

  // Finalization date for financial data
  const finalizationDate = new Date("2025-07-25")

  return (
    <>
      <BusinessOverview />

      <h2 className="mt-2 -mb-1 flex flex-wrap items-baseline gap-x-4 gap-y-2 text-2xl leading-none font-medium">
        Profit & Loss
        <span className="text-primary text-sm">
          Data snapshot as of 28 May 2025
        </span>
      </h2>

      <Notice defaultShow={defaultShow} finalizationDate={finalizationDate} />

      <div className="grid gap-4 lg:grid-cols-2">
        <Revenue />
        <EBITDA />
        <Profit />
        <RevenueEBITDAProfitMargin />
        <RevenueBreakdown />
        <RevenueBreakdownBySegment />
        <EBITDABreakdownBySegment />
        <ProfitBreakdownBySegment />
      </div>
    </>
  )
}

export default ProfitAndLoss
