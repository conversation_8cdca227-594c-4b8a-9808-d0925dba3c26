import React from "react"
import { cookies } from "next/headers"

import DataSnapshotDate from "@/components/layouts/DataSnapshotDate"
import Notice from "@/components/layouts/Notice"
import HTMLViewer from "@/components/summary/insights/HTMLViewer"

export const metadata = {
  title: "Insights | Summary",
}

const Insights = async () => {
  const cookieStore = await cookies()
  const noticeState = cookieStore.get("notice_state")?.value ?? "true"
  const defaultShow = noticeState === "true"

  return (
    <>
      <h2 className="mt-2 -mb-1 flex flex-wrap items-baseline gap-x-4 gap-y-2 text-2xl leading-none font-medium">
        Insights
        <DataSnapshotDate />
      </h2>

      <Notice defaultShow={defaultShow} />

      <HTMLViewer />
    </>
  )
}

export default Insights
