import React from "react"
import { cookies } from "next/headers"

import Notice from "@/components/layouts/Notice"
import BankNetDebt from "@/components/summary/credit-and-balance-sheet/BankNetDebt"
import CurrentCashBalance from "@/components/summary/credit-and-balance-sheet/CurrentCashBalance"
import FCF from "@/components/summary/credit-and-balance-sheet/FCF"
import OCF from "@/components/summary/credit-and-balance-sheet/OCF"
import TotalDebt from "@/components/summary/credit-and-balance-sheet/TotalDebt"

export const metadata = {
  title: "Credit & Balance Sheet | Summary",
}

const CreditAndBalanceSheet = async () => {
  const cookieStore = await cookies()
  const noticeState = cookieStore.get("notice_state")?.value ?? "true"
  const defaultShow = noticeState === "true"

  // Finalization date for financial data
  const finalizationDate = new Date("2025-07-25")

  return (
    <>
      <h2 className="mt-2 -mb-1 flex flex-wrap items-baseline gap-x-4 gap-y-2 text-2xl leading-none font-medium">
        Credit & Balance Sheet
        <span className="text-primary text-sm">
          Data snapshot as of 28 May 2025
        </span>
      </h2>

      <Notice defaultShow={defaultShow} finalizationDate={finalizationDate} />

      <div className="grid gap-4 lg:grid-cols-2">
        <TotalDebt />
        <BankNetDebt />
        <CurrentCashBalance />
        <OCF />
        <FCF />
      </div>
    </>
  )
}

export default CreditAndBalanceSheet
