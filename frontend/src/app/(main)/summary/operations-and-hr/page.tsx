import React from "react"
import { cookies } from "next/headers"

import Notice from "@/components/layouts/Notice"
import ClinicsRevenueAnalysis from "@/components/summary/operations-and-hr/ClinicsRevenueAnalysis"
import Doctors from "@/components/summary/operations-and-hr/Doctors"
import FTEsDemographics from "@/components/summary/operations-and-hr/FTEsDemographics"
import HeadcountDemographics from "@/components/summary/operations-and-hr/HeadcountDemographics"
import Org<PERSON>hart from "@/components/summary/operations-and-hr/OrgChart"
import Overview from "@/components/summary/operations-and-hr/Overview"
import Segments from "@/components/summary/operations-and-hr/Segments"
import StaffGrowth from "@/components/summary/operations-and-hr/StaffGrowth"

export const metadata = {
  title: "Operations & HR | Summary",
}

const OperationsAndHR = async () => {
  const cookieStore = await cookies()
  const noticeState = cookieStore.get("notice_state")?.value ?? "true"
  const defaultShow = noticeState === "true"

  // Finalization date for financial data
  const finalizationDate = new Date("2025-07-25")

  return (
    <>
      <Overview />

      <h2 className="mt-2 -mb-1 flex flex-wrap items-baseline gap-x-4 gap-y-2 text-2xl leading-none font-medium">
        Operations & HR
        <span className="text-primary text-sm">
          Data snapshot as of 28 May 2025
        </span>
      </h2>

      <Notice defaultShow={defaultShow} finalizationDate={finalizationDate} />

      <div className="grid gap-4 lg:grid-cols-2">
        <Segments />
        <Doctors />
        <div className="lg:col-span-2">
          <ClinicsRevenueAnalysis />
        </div>
        <FTEsDemographics />
        <HeadcountDemographics />
        <div className="lg:col-span-2">
          <OrgChart />
        </div>
        <div className="lg:col-span-2">
          <StaffGrowth />
        </div>
      </div>
    </>
  )
}

export default OperationsAndHR
