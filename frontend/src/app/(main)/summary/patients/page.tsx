import React from "react"
import { cookies } from "next/headers"

import Notice from "@/components/layouts/Notice"
import NewPatientCountByClinic from "@/components/summary/patients/NewPatientCountByClinic"
import PatientCountByClinic from "@/components/summary/patients/PatientCountByClinic"
import PatientVisitByClinic from "@/components/summary/patients/PatientVisitByClinic"

export const metadata = {
  title: "Patients | Summary",
}

const Patients = async () => {
  const cookieStore = await cookies()
  const noticeState = cookieStore.get("notice_state")?.value ?? "true"
  const defaultShow = noticeState === "true"

  // Finalization date for financial data
  const finalizationDate = new Date("2025-07-25")

  return (
    <>
      <h2 className="mt-2 -mb-1 flex flex-wrap items-baseline gap-x-4 gap-y-2 text-2xl leading-none font-medium">
        Patients
        <span className="text-primary text-sm">
          Data snapshot as of 28 May 2025
        </span>
      </h2>

      <Notice defaultShow={defaultShow} finalizationDate={finalizationDate} />

      <div className="grid gap-4 lg:grid-cols-2">
        <PatientCountByClinic />
        <PatientVisitByClinic />
        <NewPatientCountByClinic />
      </div>
    </>
  )
}

export default Patients
